// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"
// Establish Phoenix Socket and LiveView configuration.
import { Socket } from "phoenix"
import { LiveSocket } from "phoenix_live_view"
import topbar from "../vendor/topbar"
import setupPostExpansion from "./post_expansion"
import setupMobileMenu from "./mobile_menu"
import setupGridSizePersistence from "./grid_size"
import InfiniteScroll from "./hooks/infinite_scroll"
import Toast from "./hooks/toast"
import PostEditScroll from "./hooks/post_edit_scroll"

let csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")

// Define hooks for LiveView
const Hooks = {
  PostsContainer: {
    mounted() {
      setupPostExpansion();
      setupGridSizePersistence();

      // This handles direct navigation to a post URL
      // For direct URL access, the post is moved to the top of the list on the server side
      const handleDirectPostAccess = () => {
        // Get the current URL path
        const path = window.location.pathname;

        // Check if it's a post URL (e.g., /posts/123)
        const postMatch = path.match(/\/posts\/(\d+)/);

        if (postMatch && postMatch[1]) {
          const postId = parseInt(postMatch[1], 10);
          console.log('Direct post access detected in LiveView hook:', postId);

          // Set a flag to track this direct access
          window.directAccessPostId = postId;

          // Wait for the post to be available in the DOM and check if it needs expansion
          const checkAndExpandPost = (retryCount = 0, maxRetries = 20) => {
            if (retryCount >= maxRetries) {
              console.error(`Failed to find post ${postId} after ${maxRetries} attempts`);
              // Clear the flag even if we failed
              window.directAccessPostId = null;
              return;
            }

            const postToExpand = document.getElementById(`post-${postId}`);

            if (postToExpand) {
              // Check if the post is already expanded (has the CSS class from server)
              const hasExpandedClass = postToExpand.classList.contains('expanded-post');

              // For direct URL access, if the server has already rendered the post with expanded-post class,
              // we just need to set the internal JavaScript state - CSS handles the visual expansion
              if (hasExpandedClass) {
                console.log(`Post ${postId} has expanded-post class from server, setting internal state only`);

                // Set the internal JavaScript state to match the server-rendered state
                window.expandedPostId = postId;

                // Set a flag to indicate this post was handled by the server
                postToExpand.setAttribute('data-expanded-by-server', 'true');

                // For server-rendered posts, we only need to apply grid-row positioning
                // The CSS already handles grid-column: 1 / -1
                const postsGrid = postToExpand.closest('.posts-grid') || postToExpand.closest('.content-grid');
                if (postsGrid && window.setCorrectRowPositions) {
                  // Create a minimal version that only sets grid-row, not grid-column
                  setTimeout(() => {
                    console.log(`Applying minimal grid positioning for server-rendered post ${postId}`);

                    // Get all posts in the grid
                    const allPosts = Array.from(postsGrid.querySelectorAll('.post-card'));
                    const expandedPostIndex = allPosts.indexOf(postToExpand);

                    if (expandedPostIndex !== -1) {
                      // Calculate grid columns
                      const computedStyle = window.getComputedStyle(postsGrid);
                      const gridTemplateColumns = computedStyle.gridTemplateColumns;
                      const columnCount = gridTemplateColumns.split(' ').length;

                      // Calculate which row the expanded post should be in
                      const expandedPostRow = Math.floor(expandedPostIndex / columnCount) + 1;

                      // Only set grid-row, CSS handles grid-column
                      postToExpand.style.setProperty('grid-row', `${expandedPostRow}`, 'important');

                      // Move other posts in the same row to the next row
                      allPosts.forEach((post, index) => {
                        if (post !== postToExpand) {
                          const postRow = Math.floor(index / columnCount) + 1;
                          if (postRow === expandedPostRow) {
                            const newRow = expandedPostRow + 1;
                            post.style.setProperty('grid-row', `${newRow}`, 'important');
                          } else if (postRow > expandedPostRow) {
                            const newRow = postRow + 1;
                            post.style.setProperty('grid-row', `${newRow}`, 'important');
                          }
                        }
                      });
                    }
                  }, 50); // Minimal delay to ensure DOM is ready
                }

                // Clear the direct access flag after a delay
                setTimeout(() => {
                  console.log(`Clearing directAccessPostId flag for post ${postId} (server-rendered expansion)`);
                  window.directAccessPostId = null;
                }, 1000);

                return;
              }

              // Only if the post doesn't have the expanded-post class should we try to expand it manually
              // This handles edge cases where the server didn't render it as expanded
              const hasCorrectGridColumn = postToExpand.style.gridColumn === '1 / -1';
              const jsStateAlreadySet = window.expandedPostId === postId;
              const handledByDomLoaded = postToExpand.hasAttribute('data-expanded-by-dom-loaded');

              if (hasCorrectGridColumn || jsStateAlreadySet || handledByDomLoaded) {
                console.log(`Post ${postId} is already handled by other systems, skipping`);

                // Clear the direct access flag
                setTimeout(() => {
                  console.log(`Clearing directAccessPostId flag for post ${postId}`);
                  window.directAccessPostId = null;
                }, 1000);

                return;
              } else {
                console.log(`Post ${postId} found but not expanded by server, manually expanding`);
                // Post exists but not expanded, expand it
                setTimeout(() => {
                  window.handlePostExpansion(postId);

                  // Clear the direct access flag after expansion
                  setTimeout(() => {
                    console.log(`Clearing directAccessPostId flag for post ${postId} after manual expansion`);
                    window.directAccessPostId = null;
                  }, 1000);
                }, 100);
              }
            } else {
              console.log(`Post ${postId} not found in DOM yet, retry ${retryCount + 1}/${maxRetries}...`);
              // Try again after a short delay
              setTimeout(() => {
                checkAndExpandPost(retryCount + 1, maxRetries);
              }, 100);
            }
          };

          // Start checking for the post
          setTimeout(() => {
            checkAndExpandPost();
          }, 100);
        }
      };

      // Start handling direct post access
      handleDirectPostAccess();

      // Listen for post expansion events from the server
      this.handleEvent("post-expanded", ({ id, from_click }) => {
        console.log(`Post expansion event received for post ${id}, from_click: ${from_click}`);

        // Check if this is a direct URL access scenario
        const isDirectAccess = window.directAccessPostId === id;
        const post = document.getElementById(`post-${id}`);
        const hasExpandedClass = post && post.classList.contains('expanded-post');

        // For direct URL access with server-rendered expanded posts, skip the visual application
        // The DOMContentLoaded handler should be the only one managing the expansion
        if (isDirectAccess && hasExpandedClass) {
          console.log(`Skipping post-expanded event for direct access post ${id} - DOMContentLoaded handler manages it`);
          return;
        }

        // Update the URL to reflect the expanded post
        try {
          const currentPath = window.location.pathname;
          const expectedPath = `/posts/${id}`;

          if (currentPath !== expectedPath) {
            console.log(`Updating URL to ${expectedPath} from server event`);
            window.history.pushState({}, '', expectedPath);
          }
        } catch (e) {
          console.error('Error updating URL from server event:', e);
        }

        // Only apply visual expansion for click-based expansions or non-direct access scenarios
        console.log(`Applying visual expansion for post ${id} (from_click: ${from_click}, direct_access: ${isDirectAccess})`);
        window.applyPostExpansionVisuals(id);
      });

      // Listen for post collapse events from the server
      this.handleEvent("post-collapsed", ({ recently_saved }) => {
        console.log("Post collapse event received, recently_saved:", recently_saved);

        // Update the URL to reflect the collapsed state
        try {
          const currentPath = window.location.pathname;
          if (currentPath.match(/\/posts\/\d+/)) {
            console.log('Updating URL to / from server collapse event');
            window.history.pushState({}, '', '/');
          }
        } catch (e) {
          console.error('Error updating URL from server collapse event:', e);
        }

        // Call the collapse handler with information about whether this was recently saved
        // This helps the collapse handler decide whether to skip scroll restoration
        // Pass skipPhoenixNotification=true since Phoenix already initiated this collapse
        window.handlePostCollapse(false, recently_saved, true);
      });
    },

    // Add handlers for URL changes
    updated() {
      // Check if the URL matches a post but no post is expanded
      const path = window.location.pathname;
      const postMatch = path.match(/\/posts\/(\d+)/);

      if (postMatch && postMatch[1]) {
        const postId = parseInt(postMatch[1], 10);
        const post = document.getElementById(`post-${postId}`);

        // Only expand if the post exists, is not expanded, AND this is not a direct URL access
        // For direct URL access, the DOMContentLoaded handler should handle expansion
        if (post && !post.classList.contains('expanded-post') && !window.directAccessPostId) {
          console.log(`URL indicates post ${postId} should be expanded, but it's not. Expanding now.`);
          window.handlePostExpansion(postId);
        } else if (post && !post.classList.contains('expanded-post') && window.directAccessPostId === postId) {
          console.log(`Skipping expansion for direct access post ${postId} - DOMContentLoaded handler should handle it`);
        }
      }
    }
  },
  MobileMenu: {
    mounted() {
      console.log("MobileMenu hook mounted");
      setupMobileMenu();
    }
  },
  // Add the InfiniteScroll hook
  InfiniteScroll,
  // Add the Toast hook
  Toast,
  // Add the PostEditScroll hook
  PostEditScroll
};

let liveSocket = new LiveSocket("/live", Socket, {
  longPollFallbackMs: 2500,
  params: { _csrf_token: csrfToken },
  hooks: Hooks
})

// Show progress bar on live navigation and form submits
topbar.config({ barColors: { 0: "#29d" }, shadowColor: "rgba(0, 0, 0, .3)" })
window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

// connect if there are any LiveViews on the page
liveSocket.connect()

// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket

